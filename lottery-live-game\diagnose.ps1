# 系统环境诊断工具
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "系统环境诊断工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "[检查1] PHP环境" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

try {
    $phpVersion = php --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ PHP已安装" -ForegroundColor Green
        Write-Host $phpVersion.Split("`n")[0] -ForegroundColor Gray
        
        # 检查PHP扩展
        $extensions = php -m 2>$null
        $requiredExtensions = @("pdo_mysql", "mysqli", "openssl", "curl", "mbstring")
        $missingExtensions = @()
        
        foreach ($ext in $requiredExtensions) {
            if ($extensions -notcontains $ext) {
                $missingExtensions += $ext
            }
        }
        
        if ($missingExtensions.Count -eq 0) {
            Write-Host "✓ PHP扩展正常" -ForegroundColor Green
        } else {
            Write-Host "✗ 缺少PHP扩展: $($missingExtensions -join ', ')" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ PHP未安装或未添加到PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "解决方案：" -ForegroundColor Yellow
    Write-Host "1. 安装XAMPP: https://www.apachefriends.org/" -ForegroundColor White
    Write-Host "2. 或手动安装PHP: https://windows.php.net/download/" -ForegroundColor White
    Write-Host "3. 将PHP路径添加到系统PATH环境变量" -ForegroundColor White
}

Write-Host ""
Write-Host "[检查2] Composer环境" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

try {
    $composerVersion = composer --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Composer已安装" -ForegroundColor Green
        Write-Host $composerVersion -ForegroundColor Gray
    }
} catch {
    Write-Host "✗ Composer未安装" -ForegroundColor Red
    Write-Host ""
    Write-Host "解决方案：" -ForegroundColor Yellow
    Write-Host "下载安装: https://getcomposer.org/download/" -ForegroundColor White
}

Write-Host ""
Write-Host "[检查3] MySQL环境" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

try {
    $mysqlVersion = mysql --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ MySQL客户端已安装" -ForegroundColor Green
        Write-Host $mysqlVersion -ForegroundColor Gray
    }
} catch {
    Write-Host "✗ MySQL客户端未安装" -ForegroundColor Red
    Write-Host ""
    Write-Host "解决方案：" -ForegroundColor Yellow
    Write-Host "1. 安装XAMPP (包含MySQL)" -ForegroundColor White
    Write-Host "2. 或安装MySQL Community Server" -ForegroundColor White
}

Write-Host ""
Write-Host "[检查4] 项目文件" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

if (Test-Path "artisan") {
    Write-Host "✓ Laravel项目文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ Laravel项目文件缺失" -ForegroundColor Red
}

if (Test-Path "vendor") {
    Write-Host "✓ vendor目录存在" -ForegroundColor Green
} else {
    Write-Host "✗ vendor目录不存在，需要运行: composer install" -ForegroundColor Red
}

if (Test-Path ".env") {
    Write-Host "✓ .env配置文件存在" -ForegroundColor Green
    
    $envContent = Get-Content ".env" -Raw
    if ($envContent -match "APP_KEY=.+") {
        Write-Host "✓ APP_KEY已设置" -ForegroundColor Green
    } else {
        Write-Host "✗ APP_KEY未设置，需要运行: php artisan key:generate" -ForegroundColor Red
    }
} else {
    Write-Host "✗ .env配置文件不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "[检查5] 端口占用" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

$port8000 = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
if ($port8000) {
    Write-Host "✗ 端口8000已被占用" -ForegroundColor Red
} else {
    Write-Host "✓ 端口8000可用" -ForegroundColor Green
}

$port8080 = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
if ($port8080) {
    Write-Host "✗ 端口8080已被占用" -ForegroundColor Red
} else {
    Write-Host "✓ 端口8080可用" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "诊断完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "根据上面的检查结果：" -ForegroundColor Yellow
Write-Host "1. 如果PHP未安装，请先安装PHP环境" -ForegroundColor White
Write-Host "2. 如果Composer未安装，请安装Composer" -ForegroundColor White
Write-Host "3. 如果vendor目录不存在，运行: composer install" -ForegroundColor White
Write-Host "4. 如果APP_KEY未设置，运行: php artisan key:generate" -ForegroundColor White
Write-Host "5. 如果端口被占用，请关闭占用程序或修改配置" -ForegroundColor White
Write-Host ""
Write-Host "详细安装指南请查看: INSTALL-GUIDE.md" -ForegroundColor Cyan
Write-Host ""

Read-Host "按回车键继续"
