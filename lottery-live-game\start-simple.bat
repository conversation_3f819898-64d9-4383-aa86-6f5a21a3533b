@echo off
echo ========================================
echo 彩票直播游戏系统 - 简化启动
echo ========================================

echo.
echo 正在启动Laravel开发服务器...
start "Laravel服务器" cmd /k "php artisan serve"

echo.
echo 等待3秒...
timeout /t 3 /nobreak > nul

echo.
echo 正在启动WebSocket服务器...
start "WebSocket服务器" cmd /k "php bin/websocket-server.php"

echo.
echo 等待3秒...
timeout /t 3 /nobreak > nul

echo.
echo 正在启动视频流服务器...
start "视频流服务器" cmd /k "php bin/video-stream-server.php"

echo.
echo ========================================
echo 服务器启动完成！
echo ========================================
echo.
echo 前台游戏: http://localhost:8000
echo 测试账号: 6117 / 6677
echo.
echo 后台管理: http://localhost:8000/admin
echo 管理账号: admin / admin123
echo.
echo 注意：如果是第一次运行，请先执行：
echo 1. php artisan key:generate
echo 2. php artisan migrate
echo 3. php artisan db:seed
echo.
pause
