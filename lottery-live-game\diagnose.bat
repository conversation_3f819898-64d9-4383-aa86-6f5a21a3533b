@echo off
echo ========================================
echo 系统环境诊断工具
echo ========================================

echo.
echo [检查1] PHP环境
echo ----------------------------------------
php --version 2>nul
if %errorlevel% equ 0 (
    echo ✓ PHP已安装
    php -m | findstr /i "pdo_mysql mysqli openssl curl"
    if %errorlevel% equ 0 (
        echo ✓ PHP扩展正常
    ) else (
        echo ✗ 缺少必要的PHP扩展
    )
) else (
    echo ✗ PHP未安装或未添加到PATH
    echo.
    echo 解决方案：
    echo 1. 安装XAMPP: https://www.apachefriends.org/
    echo 2. 或手动安装PHP: https://windows.php.net/download/
    echo 3. 将PHP路径添加到系统PATH环境变量
)

echo.
echo [检查2] Composer环境
echo ----------------------------------------
composer --version 2>nul
if %errorlevel% equ 0 (
    echo ✓ Composer已安装
) else (
    echo ✗ Composer未安装
    echo.
    echo 解决方案：
    echo 下载安装: https://getcomposer.org/download/
)

echo.
echo [检查3] MySQL环境
echo ----------------------------------------
mysql --version 2>nul
if %errorlevel% equ 0 (
    echo ✓ MySQL客户端已安装
) else (
    echo ✗ MySQL客户端未安装
    echo.
    echo 解决方案：
    echo 1. 安装XAMPP (包含MySQL)
    echo 2. 或安装MySQL Community Server
)

echo.
echo [检查4] 项目文件
echo ----------------------------------------
if exist "artisan" (
    echo ✓ Laravel项目文件存在
) else (
    echo ✗ Laravel项目文件缺失
)

if exist "vendor" (
    echo ✓ vendor目录存在
) else (
    echo ✗ vendor目录不存在，需要运行: composer install
)

if exist ".env" (
    echo ✓ .env配置文件存在
    findstr /i "APP_KEY=" .env | findstr /v "APP_KEY=$" >nul
    if %errorlevel% equ 0 (
        echo ✓ APP_KEY已设置
    ) else (
        echo ✗ APP_KEY未设置，需要运行: php artisan key:generate
    )
) else (
    echo ✗ .env配置文件不存在
)

echo.
echo [检查5] 端口占用
echo ----------------------------------------
netstat -an | findstr ":8000" >nul
if %errorlevel% equ 0 (
    echo ✗ 端口8000已被占用
) else (
    echo ✓ 端口8000可用
)

netstat -an | findstr ":8080" >nul
if %errorlevel% equ 0 (
    echo ✗ 端口8080已被占用
) else (
    echo ✓ 端口8080可用
)

echo.
echo ========================================
echo 诊断完成
echo ========================================
echo.
echo 根据上面的检查结果：
echo 1. 如果PHP未安装，请先安装PHP环境
echo 2. 如果Composer未安装，请安装Composer
echo 3. 如果vendor目录不存在，运行: composer install
echo 4. 如果APP_KEY未设置，运行: php artisan key:generate
echo 5. 如果端口被占用，请关闭占用程序或修改配置
echo.
echo 详细安装指南请查看: INSTALL-GUIDE.md
echo.
pause
