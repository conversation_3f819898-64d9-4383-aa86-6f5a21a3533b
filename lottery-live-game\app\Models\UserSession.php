<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'last_activity',
        'is_online',
    ];

    protected function casts(): array
    {
        return [
            'last_activity' => 'datetime',
            'is_online' => 'boolean',
        ];
    }

    // 关联关系
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 业务方法
    public function updateActivity()
    {
        $this->update([
            'last_activity' => now(),
            'is_online' => true,
        ]);
    }

    public function setOffline()
    {
        $this->update(['is_online' => false]);
    }

    public function scopeOnline($query)
    {
        return $query->where('is_online', true)
                    ->where('last_activity', '>', now()->subMinutes(5));
    }
}
