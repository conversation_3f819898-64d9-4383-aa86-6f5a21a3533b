@extends('admin.layout')

@section('title', '用户管理')

@section('content')
<!-- 搜索和过滤 -->
<div style="margin-bottom: 20px; display: flex; gap: 15px; align-items: center;">
    <form method="GET" style="display: flex; gap: 10px; align-items: center;">
        <input type="text" name="search" value="{{ request('search') }}" placeholder="搜索用户名" class="form-control" style="width: 200px;">
        
        <select name="status" class="form-control" style="width: 120px;">
            <option value="">全部状态</option>
            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>正常</option>
            <option value="banned" {{ request('status') === 'banned' ? 'selected' : '' }}>禁用</option>
            <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>暂停</option>
        </select>
        
        <button type="submit" class="btn btn-primary">搜索</button>
        <a href="{{ url(env('ADMIN_PATH', 'admin') . '/users') }}" class="btn btn-warning">重置</a>
    </form>
</div>

<!-- 用户列表 -->
<table class="table">
    <thead>
        <tr>
            <th>用户ID</th>
            <th>用户名</th>
            <th>余额</th>
            <th>状态</th>
            <th>投注次数</th>
            <th>投注总额</th>
            <th>注册时间</th>
            <th>最后登录</th>
            <th>在线状态</th>
            <th>操作</th>
        </tr>
    </thead>
    <tbody>
        @foreach($users as $user)
        <tr>
            <td>#{{ $user->id }}</td>
            <td>{{ $user->username }}</td>
            <td>¥{{ number_format($user->balance, 2) }}</td>
            <td>
                @if($user->status === 'active')
                    <span class="status-badge status-active">正常</span>
                @elseif($user->status === 'banned')
                    <span class="status-badge status-banned">禁用</span>
                @else
                    <span class="status-badge status-pending">{{ $user->status }}</span>
                @endif
            </td>
            <td>{{ number_format($user->bets_count) }}</td>
            <td>¥{{ number_format($user->bets_sum_bet_amount ?? 0, 2) }}</td>
            <td>{{ $user->created_at->format('Y-m-d H:i') }}</td>
            <td>{{ $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i') : '从未登录' }}</td>
            <td>
                @if($user->isOnline())
                    <span class="status-badge status-win">在线</span>
                @else
                    <span class="status-badge">离线</span>
                @endif
            </td>
            <td>
                <a href="{{ url(env('ADMIN_PATH', 'admin') . '/users/' . $user->id) }}" class="btn btn-primary">详情</a>
                
                @if($user->status === 'active')
                    <button onclick="changeUserStatus({{ $user->id }}, 'banned')" class="btn btn-danger">禁用</button>
                @else
                    <button onclick="changeUserStatus({{ $user->id }}, 'active')" class="btn btn-success">启用</button>
                @endif
            </td>
        </tr>
        @endforeach
    </tbody>
</table>

<!-- 分页 -->
<div class="pagination">
    {{ $users->links() }}
</div>

<script>
    // 修改用户状态
    async function changeUserStatus(userId, status) {
        const statusText = status === 'banned' ? '禁用' : '启用';
        
        if (!confirm(`确定要${statusText}这个用户吗？`)) {
            return;
        }
        
        try {
            const response = await fetch(`{{ url(env('ADMIN_PATH', 'admin') . '/users') }}/${userId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ status: status })
            });
            
            if (response.ok) {
                location.reload();
            } else {
                alert('操作失败，请稍后重试');
            }
        } catch (error) {
            console.error('操作失败:', error);
            alert('操作失败，请稍后重试');
        }
    }
</script>
@endsection
