<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('game_id')->constrained()->onDelete('cascade');
            $table->string('bet_type', 20); // 'small', 'big', 'odd', 'even', 'number', 'range'
            $table->string('bet_value', 10)->nullable(); // 具体投注值
            $table->decimal('bet_amount', 8, 2);
            $table->decimal('odds', 4, 2);
            $table->decimal('potential_win', 10, 2);
            $table->enum('status', ['pending', 'win', 'lose', 'cancelled'])->default('pending');
            $table->timestamps();
            
            $table->index(['user_id', 'game_id']);
            $table->index(['game_id', 'status']);
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bets');
    }
};
