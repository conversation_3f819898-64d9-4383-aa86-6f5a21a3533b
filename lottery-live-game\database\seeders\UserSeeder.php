<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // 创建测试用户
        User::create([
            'username' => '6117',
            'password' => Hash::make('6677'),
            'balance' => 10000.00,
            'status' => 'active',
        ]);

        User::create([
            'username' => 'admin',
            'password' => Hash::make('admin123'),
            'balance' => 50000.00,
            'status' => 'active',
        ]);

        User::create([
            'username' => 'test1',
            'password' => Hash::make('123456'),
            'balance' => 5000.00,
            'status' => 'active',
        ]);

        User::create([
            'username' => 'test2',
            'password' => Hash::make('123456'),
            'balance' => 3000.00,
            'status' => 'active',
        ]);

        echo "测试用户创建成功:\n";
        echo "- 用户名: 6117, 密码: 6677, 余额: 10000\n";
        echo "- 用户名: admin, 密码: admin123, 余额: 50000\n";
        echo "- 用户名: test1, 密码: 123456, 余额: 5000\n";
        echo "- 用户名: test2, 密码: 123456, 余额: 3000\n";
    }
}
