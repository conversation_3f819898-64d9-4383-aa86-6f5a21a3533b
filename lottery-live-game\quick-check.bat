@echo off
echo ========================================
echo 快速环境检查
echo ========================================

echo.
echo 检查PHP...
where php >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ PHP已安装
    php --version
) else (
    echo ✗ PHP未安装
    echo.
    echo 请安装PHP环境：
    echo 1. 下载XAMPP: https://www.apachefriends.org/
    echo 2. 安装后将PHP添加到PATH环境变量
    echo 3. 重启命令行窗口
    goto :end
)

echo.
echo 检查Composer...
where composer >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Composer已安装
) else (
    echo ✗ Composer未安装
    echo 请从 https://getcomposer.org/ 下载安装
    goto :end
)

echo.
echo 检查项目文件...
if exist "artisan" (
    echo ✓ Laravel项目文件存在
) else (
    echo ✗ Laravel项目文件缺失
    goto :end
)

if exist "vendor" (
    echo ✓ vendor目录存在
) else (
    echo ! vendor目录不存在，正在安装依赖...
    composer install
    if %errorlevel% neq 0 (
        echo ✗ 依赖安装失败
        goto :end
    )
)

if exist ".env" (
    echo ✓ .env文件存在
) else (
    echo ! .env文件不存在，正在创建...
    copy ".env.example" ".env"
)

echo.
echo ========================================
echo 环境检查完成！
echo ========================================
echo.
echo 现在可以运行：
echo 1. setup.bat      (初始化数据库)
echo 2. start-simple.bat (启动服务器)
echo.

:end
pause
