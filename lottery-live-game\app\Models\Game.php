<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    use HasFactory;

    protected $fillable = [
        'round_number',
        'version_number',
        'result_numbers',
        'start_time',
        'end_time',
        'status',
        'total_bets',
        'total_players',
    ];

    protected function casts(): array
    {
        return [
            'result_numbers' => 'array',
            'start_time' => 'datetime',
            'end_time' => 'datetime',
            'total_bets' => 'decimal:2',
        ];
    }

    // 关联关系
    public function bets()
    {
        return $this->hasMany(Bet::class);
    }

    // 业务方法
    public function isActive()
    {
        return in_array($this->status, ['waiting', 'betting']);
    }

    public function canBet()
    {
        return $this->status === 'betting' && now()->lt($this->end_time);
    }

    public function getRemainingTimeAttribute()
    {
        if ($this->status !== 'betting') {
            return 0;
        }
        
        return max(0, $this->end_time->diffInSeconds(now()));
    }

    public function generateResult()
    {
        // 生成27个随机号码 (0-24)
        $numbers = [];
        for ($i = 0; $i < 27; $i++) {
            $numbers[] = rand(0, 24);
        }
        
        $this->update([
            'result_numbers' => $numbers,
            'status' => 'finished'
        ]);
        
        return $numbers;
    }

    public function calculateWinnings()
    {
        if (!$this->result_numbers || $this->status !== 'finished') {
            return;
        }

        $this->bets()->where('status', 'pending')->chunk(100, function ($bets) {
            foreach ($bets as $bet) {
                $isWin = $this->checkBetWin($bet);
                $bet->update([
                    'status' => $isWin ? 'win' : 'lose'
                ]);

                if ($isWin) {
                    $bet->user->addBalance($bet->potential_win, "游戏{$this->id}中奖");
                }
            }
        });
    }

    private function checkBetWin($bet)
    {
        $numbers = $this->result_numbers;
        
        switch ($bet->bet_type) {
            case 'small': // 1-12
                return collect($numbers)->filter(fn($n) => $n >= 1 && $n <= 12)->count() > 13;
            case 'big': // 13-24
                return collect($numbers)->filter(fn($n) => $n >= 13 && $n <= 24)->count() > 13;
            case 'odd': // 单数
                return collect($numbers)->filter(fn($n) => $n % 2 === 1)->count() > 13;
            case 'even': // 双数
                return collect($numbers)->filter(fn($n) => $n % 2 === 0)->count() > 13;
            case 'number': // 特定数字
                return collect($numbers)->filter(fn($n) => $n == $bet->bet_value)->count() > 0;
            case 'range': // 数字范围
                [$min, $max] = explode('-', $bet->bet_value);
                return collect($numbers)->filter(fn($n) => $n >= $min && $n <= $max)->count() > 13;
            default:
                return false;
        }
    }
}
