<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'game_id',
        'bet_type',
        'bet_value',
        'bet_amount',
        'odds',
        'potential_win',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'bet_amount' => 'decimal:2',
            'odds' => 'decimal:2',
            'potential_win' => 'decimal:2',
        ];
    }

    // 关联关系
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function game()
    {
        return $this->belongsTo(Game::class);
    }

    // 业务方法
    public function calculatePotentialWin()
    {
        return $this->bet_amount * $this->odds;
    }

    public function getBetTypeNameAttribute()
    {
        $types = [
            'small' => '小 (1-12)',
            'big' => '大 (13-24)',
            'odd' => '单',
            'even' => '双',
            'number' => '数字',
            'range' => '范围',
        ];

        return $types[$this->bet_type] ?? $this->bet_type;
    }

    public function getStatusNameAttribute()
    {
        $statuses = [
            'pending' => '等待开奖',
            'win' => '中奖',
            'lose' => '未中奖',
            'cancelled' => '已取消',
        ];

        return $statuses[$this->status] ?? $this->status;
    }
}
