# 🚀 彩票直播游戏系统 - 安装指南

## ❗ 当前问题
系统检测到PHP环境未正确配置，需要先安装PHP和相关环境。

## 📋 环境要求
- PHP 8.2 或更高版本
- MySQL 8.0 或更高版本
- Composer (PHP包管理器)

## 🛠️ 安装步骤

### 方法一：使用XAMPP (推荐新手)

1. **下载XAMPP**
   - 访问：https://www.apachefriends.org/download.html
   - 下载PHP 8.2版本的XAMPP

2. **安装XAMPP**
   - 运行下载的安装程序
   - 选择安装Apache、MySQL、PHP
   - 安装到默认路径 `C:\xampp`

3. **启动服务**
   - 打开XAMPP控制面板
   - 启动Apache和MySQL服务

4. **配置环境变量**
   - 将 `C:\xampp\php` 添加到系统PATH
   - 重启命令行窗口

5. **安装Composer**
   - 访问：https://getcomposer.org/download/
   - 下载并安装Composer-Setup.exe

### 方法二：手动安装PHP

1. **下载PHP**
   - 访问：https://windows.php.net/download/
   - 下载PHP 8.2 Thread Safe版本
   - 解压到 `C:\php`

2. **配置PHP**
   - 复制 `php.ini-development` 为 `php.ini`
   - 启用必要的扩展：
     ```ini
     extension=pdo_mysql
     extension=mysqli
     extension=openssl
     extension=curl
     extension=gd
     extension=mbstring
     extension=zip
     ```

3. **添加到PATH**
   - 将 `C:\php` 添加到系统环境变量PATH
   - 重启命令行

4. **安装MySQL**
   - 下载MySQL Community Server
   - 创建数据库用户：
     ```sql
     CREATE DATABASE xin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
     CREATE USER 'xin'@'localhost' IDENTIFIED BY 'xin00';
     GRANT ALL PRIVILEGES ON xin.* TO 'xin'@'localhost';
     FLUSH PRIVILEGES;
     ```

5. **安装Composer**
   - 下载：https://getcomposer.org/download/
   - 安装到系统

## 🔧 验证安装

安装完成后，在命令行中运行：

```bash
# 检查PHP版本
php --version

# 检查Composer
composer --version

# 检查MySQL连接
mysql -u xin -p
```

## 🚀 启动项目

环境配置完成后：

```bash
# 1. 初始化项目
setup.bat

# 2. 启动服务器
start-simple.bat
```

## 🌐 访问地址

- **前台游戏**: http://localhost:8000
- **后台管理**: http://localhost:8000/admin

## 👤 测试账号

### 前台用户
- 用户名：6117
- 密码：6677

### 后台管理员
- 用户名：admin
- 密码：admin123

## 🐛 常见问题

### Q: PHP命令不识别
A: 确保PHP路径已添加到系统PATH环境变量，并重启命令行

### Q: 数据库连接失败
A: 检查MySQL服务是否启动，数据库和用户是否正确创建

### Q: Composer安装失败
A: 确保网络连接正常，可以尝试使用国内镜像

### Q: 端口被占用
A: 修改.env文件中的端口配置，或关闭占用端口的程序

## 📞 技术支持

如果遇到问题，请检查：
1. PHP版本是否为8.2+
2. MySQL服务是否正常运行
3. 防火墙是否阻止了端口访问
4. .env文件配置是否正确

## 🎯 快速测试

如果只想快速测试，可以：
1. 安装XAMPP
2. 启动Apache和MySQL
3. 运行 `setup.bat`
4. 运行 `start-simple.bat`

---

**注意**: 本系统仅用于学习和演示目的，请遵守当地法律法规。
