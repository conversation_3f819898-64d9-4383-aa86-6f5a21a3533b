@echo off
echo ========================================
echo 彩票直播游戏系统启动脚本
echo ========================================

echo.
echo [1/4] 检查PHP环境...
php --version
if %errorlevel% neq 0 (
    echo 错误: PHP未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo [2/4] 生成应用密钥...
php artisan key:generate

echo.
echo [3/4] 运行数据库迁移...
php artisan migrate --force

echo.
echo [4/4] 启动服务器...

echo.
echo 启动WebSocket服务器 (端口8080)...
start "WebSocket服务器" cmd /k "php bin/websocket-server.php"

echo.
echo 等待2秒...
timeout /t 2 /nobreak > nul

echo.
echo 启动视频流服务器 (模拟摄像头)...
start "视频流服务器" cmd /k "php bin/video-stream-server.php"

echo.
echo 等待2秒...
timeout /t 2 /nobreak > nul

echo.
echo 启动Laravel开发服务器 (端口8000)...
start "Laravel服务器" cmd /k "php artisan serve"

echo.
echo ========================================
echo 所有服务器启动完成！
echo ========================================
echo.
echo 访问地址: http://localhost:8000
echo 测试账号: 6117
echo 测试密码: 6677
echo.
echo WebSocket服务器: ws://localhost:8080
echo 视频流API: http://localhost:8081
echo.
echo 按任意键退出...
pause > nul
