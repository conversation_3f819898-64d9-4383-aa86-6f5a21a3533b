<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    public function loginPage()
    {
        if (Auth::check()) {
            return redirect('/game');
        }
        
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $user = User::where('username', $request->username)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'code' => 400,
                'msg' => '用户名或密码错误',
                'status' => 400
            ]);
        }

        if ($user->status !== 'active') {
            return response()->json([
                'code' => 403,
                'msg' => '账户已被禁用',
                'status' => 403
            ]);
        }

        // 更新登录信息
        $user->update([
            'last_login_at' => now(),
            'last_login_ip' => $request->ip(),
        ]);

        // 创建会话记录
        $sessionId = Str::random(128);
        UserSession::create([
            'user_id' => $user->id,
            'session_id' => $sessionId,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'last_activity' => now(),
            'is_online' => true,
        ]);

        // 登录用户
        Auth::login($user, $request->has('remember'));
        
        // 将session_id存储到session中
        session(['user_session_id' => $sessionId]);

        return response()->json([
            'code' => 200,
            'msg' => '登录成功',
            'status' => 200,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'balance' => $user->balance,
                ]
            ]
        ]);
    }

    public function logout(Request $request)
    {
        $sessionId = session('user_session_id');
        if ($sessionId) {
            UserSession::where('session_id', $sessionId)->update(['is_online' => false]);
        }

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json([
            'code' => 200,
            'msg' => '退出成功',
            'status' => 200
        ]);
    }

    public function register(Request $request)
    {
        $request->validate([
            'username' => 'required|string|unique:users|min:4|max:20',
            'password' => 'required|string|min:6',
        ]);

        $user = User::create([
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'balance' => 1000.00, // 新用户赠送1000积分
            'status' => 'active',
        ]);

        return response()->json([
            'code' => 200,
            'msg' => '注册成功',
            'status' => 200,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'balance' => $user->balance,
                ]
            ]
        ]);
    }
}
