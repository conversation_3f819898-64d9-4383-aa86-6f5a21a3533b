@echo off
echo ========================================
echo 彩票直播游戏系统 - 初始化设置
echo ========================================

echo.
echo [1/5] 生成应用密钥...
php artisan key:generate
if %errorlevel% neq 0 (
    echo ✗ 应用密钥生成失败
    echo 请检查PHP是否正确安装
    pause
    exit /b 1
)

echo.
echo [2/5] 检查数据库连接...
php check-database.php
if %errorlevel% neq 0 (
    echo ✗ 数据库连接失败
    echo 请检查MySQL服务和数据库配置
    pause
    exit /b 1
)

echo.
echo [3/5] 运行数据库迁移...
php artisan migrate --force
if %errorlevel% neq 0 (
    echo ✗ 数据库迁移失败
    pause
    exit /b 1
)

echo.
echo [4/5] 创建测试用户...
php artisan db:seed --class=UserSeeder
if %errorlevel% neq 0 (
    echo ✗ 测试用户创建失败
    pause
    exit /b 1
)

echo.
echo [5/5] 设置存储权限...
if not exist "storage\logs" mkdir "storage\logs"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"

echo.
echo ========================================
echo 初始化完成！
echo ========================================
echo.
echo 现在可以运行: start-simple.bat
echo.
echo 测试账号:
echo - 用户: 6117 / 6677
echo - 管理员: admin / admin123
echo.
pause
