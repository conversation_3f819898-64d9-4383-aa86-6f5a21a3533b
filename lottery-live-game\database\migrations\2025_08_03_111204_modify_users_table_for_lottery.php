<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 添加用户名字段
            $table->string('username', 50)->unique()->after('id');
            // 添加余额字段
            $table->decimal('balance', 10, 2)->default(0.00)->after('password');
            // 添加状态字段
            $table->enum('status', ['active', 'banned', 'suspended'])->default('active')->after('balance');
            // 添加最后登录时间
            $table->timestamp('last_login_at')->nullable()->after('status');
            // 添加最后登录IP
            $table->string('last_login_ip', 45)->nullable()->after('last_login_at');

            // 删除不需要的字段
            $table->dropColumn(['name', 'email', 'email_verified_at']);
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 恢复原始字段
            $table->string('name')->after('id');
            $table->string('email')->unique()->after('name');
            $table->timestamp('email_verified_at')->nullable()->after('email');

            // 删除添加的字段
            $table->dropColumn(['username', 'balance', 'status', 'last_login_at', 'last_login_ip']);
        });
    }
};
