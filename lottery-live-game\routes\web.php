<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\GameController;

// 认证路由
Route::get('/', [AuthController::class, 'loginPage'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout']);
Route::post('/register', [AuthController::class, 'register']);

// 游戏路由 (需要登录)
Route::middleware('auth')->group(function () {
    Route::get('/game', [GameController::class, 'index'])->name('game');
    Route::get('/api/game/status', [GameController::class, 'getStatus']);
    Route::post('/api/game/bet', [GameController::class, 'placeBet']);
});
