<?php

/**
 * 数据库连接检查脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载环境变量
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== 数据库连接检查 ===\n\n";

// 获取数据库配置
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$port = $_ENV['DB_PORT'] ?? '3306';
$database = $_ENV['DB_DATABASE'] ?? 'xin';
$username = $_ENV['DB_USERNAME'] ?? 'xin';
$password = $_ENV['DB_PASSWORD'] ?? 'xin00';

echo "数据库配置:\n";
echo "- 主机: {$host}:{$port}\n";
echo "- 数据库: {$database}\n";
echo "- 用户名: {$username}\n";
echo "- 密码: " . str_repeat('*', strlen($password)) . "\n\n";

try {
    // 尝试连接MySQL服务器
    echo "1. 检查MySQL服务器连接...\n";
    $pdo = new PDO("mysql:host={$host};port={$port}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ MySQL服务器连接成功\n\n";
    
    // 检查数据库是否存在
    echo "2. 检查数据库是否存在...\n";
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$database}'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        echo "   ✓ 数据库 '{$database}' 已存在\n\n";
    } else {
        echo "   ✗ 数据库 '{$database}' 不存在\n";
        echo "   正在创建数据库...\n";
        
        $pdo->exec("CREATE DATABASE `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "   ✓ 数据库 '{$database}' 创建成功\n\n";
    }
    
    // 尝试连接到指定数据库
    echo "3. 检查数据库访问权限...\n";
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ 数据库访问权限正常\n\n";
    
    // 检查表是否存在
    echo "4. 检查数据表...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "   ! 数据库为空，需要运行迁移\n";
        echo "   请执行: php artisan migrate\n\n";
    } else {
        echo "   ✓ 发现 " . count($tables) . " 个数据表:\n";
        foreach ($tables as $table) {
            echo "     - {$table}\n";
        }
        echo "\n";
    }
    
    echo "=== 数据库检查完成 ===\n";
    echo "数据库配置正确，可以正常使用！\n";
    
} catch (PDOException $e) {
    echo "   ✗ 数据库连接失败: " . $e->getMessage() . "\n\n";
    
    echo "=== 解决方案 ===\n";
    echo "1. 确保MySQL服务器正在运行\n";
    echo "2. 检查 .env 文件中的数据库配置\n";
    echo "3. 确保数据库用户有足够的权限\n";
    echo "4. 如果数据库不存在，请手动创建:\n";
    echo "   CREATE DATABASE `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
    echo "5. 如果用户不存在，请创建用户并授权:\n";
    echo "   CREATE USER '{$username}'@'localhost' IDENTIFIED BY '{$password}';\n";
    echo "   GRANT ALL PRIVILEGES ON `{$database}`.* TO '{$username}'@'localhost';\n";
    echo "   FLUSH PRIVILEGES;\n\n";
    
    exit(1);
} catch (Exception $e) {
    echo "   ✗ 发生错误: " . $e->getMessage() . "\n\n";
    exit(1);
}
