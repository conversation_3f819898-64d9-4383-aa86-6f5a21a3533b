<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Workerman\Worker;
use Workerman\Protocols\Http;
use Workerman\Connection\TcpConnection;

// WebSocket服务器 - 处理客户端连接
$ws_worker = new Worker("websocket://0.0.0.0:8080");
$ws_worker->count = 4;
$ws_worker->name = 'LotteryWebSocket';

// 存储所有连接
global $connections;
$connections = [];

// 客户端连接时
$ws_worker->onConnect = function(TcpConnection $connection) {
    global $connections;
    $connections[$connection->id] = $connection;
    
    echo "[" . date('Y-m-d H:i:s') . "] 新用户连接: {$connection->id} (总连接数: " . count($connections) . ")\n";
    
    // 发送欢迎消息
    $connection->send(json_encode([
        'type' => 'welcome',
        'data' => [
            'connection_id' => $connection->id,
            'server_time' => time(),
            'online_users' => count($connections)
        ]
    ]));
};

// 收到客户端消息时
$ws_worker->onMessage = function(TcpConnection $connection, $data) {
    global $connections;
    
    $message = json_decode($data, true);
    if (!$message) {
        return;
    }
    
    switch ($message['type']) {
        case 'heartbeat':
            // 心跳响应
            $connection->send(json_encode([
                'type' => 'pong',
                'timestamp' => microtime(true)
            ]));
            break;
            
        case 'join_game':
            // 用户加入游戏
            $connection->user_id = $message['user_id'] ?? null;
            echo "[" . date('Y-m-d H:i:s') . "] 用户 {$connection->user_id} 加入游戏\n";
            break;
            
        case 'bet':
            // 处理下注（这里只是广播，实际下注逻辑在HTTP API中）
            broadcastToAll([
                'type' => 'new_bet',
                'data' => [
                    'user_id' => $connection->user_id,
                    'bet_type' => $message['bet_type'],
                    'bet_amount' => $message['bet_amount'],
                    'timestamp' => time()
                ]
            ], $connections);
            break;
    }
};

// 客户端断开连接时
$ws_worker->onClose = function(TcpConnection $connection) {
    global $connections;
    unset($connections[$connection->id]);
    
    echo "[" . date('Y-m-d H:i:s') . "] 用户断开: {$connection->id} (剩余连接数: " . count($connections) . ")\n";
    
    // 广播在线人数更新
    broadcastToAll([
        'type' => 'online_update',
        'data' => ['online_users' => count($connections)]
    ], $connections);
};

// HTTP服务器 - 接收视频帧和游戏事件
$http_worker = new Worker("http://0.0.0.0:8081");
$http_worker->count = 2;
$http_worker->name = 'LotteryHTTP';

$http_worker->onMessage = function(TcpConnection $connection, $request) {
    global $connections;
    
    // 设置CORS头
    $headers = [
        'Access-Control-Allow-Origin' => '*',
        'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers' => 'Content-Type',
    ];
    
    if ($request->method() === 'OPTIONS') {
        Http::header($headers);
        $connection->send('');
        return;
    }
    
    $path = $request->path();
    
    switch ($path) {
        case '/broadcast-frame':
            // 接收并广播视频帧
            if ($request->method() === 'POST') {
                $frameData = $request->rawBody();
                $base64Frame = base64_encode($frameData);
                
                $message = json_encode([
                    'type' => 'video_frame',
                    'data' => $base64Frame,
                    'timestamp' => microtime(true)
                ]);
                
                broadcastToAll($message, $connections, false);
                
                Http::header($headers);
                $connection->send('OK');
            }
            break;
            
        case '/game-event':
            // 接收游戏事件
            if ($request->method() === 'POST') {
                $eventData = json_decode($request->rawBody(), true);
                
                broadcastToAll([
                    'type' => 'game_event',
                    'data' => $eventData
                ], $connections);
                
                Http::header($headers);
                $connection->send('OK');
            }
            break;
            
        case '/lottery-result':
            // 接收开奖结果
            if ($request->method() === 'POST') {
                $resultData = json_decode($request->rawBody(), true);
                
                broadcastToAll([
                    'type' => 'lottery_result',
                    'data' => $resultData
                ], $connections);
                
                Http::header($headers);
                $connection->send('OK');
            }
            break;
            
        case '/status':
            // 服务器状态
            Http::header($headers + ['Content-Type' => 'application/json']);
            $connection->send(json_encode([
                'status' => 'running',
                'connections' => count($connections),
                'memory_usage' => memory_get_usage(true),
                'uptime' => time() - WORKERMAN_START_TIME
            ]));
            break;
            
        default:
            Http::header($headers);
            $connection->send('404 Not Found');
    }
};

// 广播消息到所有连接
function broadcastToAll($message, $connections, $encode = true) {
    if (empty($connections)) {
        return;
    }
    
    $data = $encode ? json_encode($message) : $message;
    
    foreach ($connections as $conn) {
        try {
            $conn->send($data);
        } catch (Exception $e) {
            echo "[ERROR] 发送消息失败: " . $e->getMessage() . "\n";
        }
    }
}

// 定时任务 - 游戏状态更新
$timer_worker = new Worker();
$timer_worker->count = 1;
$timer_worker->name = 'LotteryTimer';

$timer_worker->onWorkerStart = function() {
    global $connections;
    
    // 每秒更新游戏状态
    \Workerman\Lib\Timer::add(1, function() use (&$connections) {
        // 这里可以添加游戏逻辑，比如倒计时、自动开奖等
        $gameStatus = [
            'type' => 'game_status',
            'data' => [
                'server_time' => time(),
                'online_users' => count($connections),
                // 这里可以添加更多游戏状态信息
            ]
        ];
        
        broadcastToAll($gameStatus, $connections);
    });
    
    echo "[" . date('Y-m-d H:i:s') . "] 定时器启动成功\n";
};

// 启动所有Worker
Worker::runAll();
