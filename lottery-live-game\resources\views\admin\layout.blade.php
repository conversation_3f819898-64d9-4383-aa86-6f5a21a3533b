<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', '后台管理') - 彩票直播游戏</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            background: #34495e;
            text-align: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: bold;
        }

        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #3498db;
            color: white;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 20px;
        }

        .content-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .content-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .content-body {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }

        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background: #d5f4e6;
            color: #27ae60;
        }

        .status-banned {
            background: #fadbd8;
            color: #e74c3c;
        }

        .status-pending {
            background: #fef9e7;
            color: #f39c12;
        }

        .status-win {
            background: #d5f4e6;
            color: #27ae60;
        }

        .status-lose {
            background: #fadbd8;
            color: #e74c3c;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid #bdc3c7;
            text-decoration: none;
            color: #2c3e50;
        }

        .pagination .current {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 消息提示 */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        .alert-error {
            background: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">后台管理</h2>
            </div>
            <nav>
                <ul class="sidebar-menu">
                    <li><a href="{{ url(env('ADMIN_PATH', 'admin')) }}" class="{{ request()->is(env('ADMIN_PATH', 'admin')) ? 'active' : '' }}">📊 仪表盘</a></li>
                    <li><a href="{{ url(env('ADMIN_PATH', 'admin') . '/users') }}" class="{{ request()->is(env('ADMIN_PATH', 'admin') . '/users*') ? 'active' : '' }}">👥 用户管理</a></li>
                    <li><a href="{{ url(env('ADMIN_PATH', 'admin') . '/bets') }}" class="{{ request()->is(env('ADMIN_PATH', 'admin') . '/bets*') ? 'active' : '' }}">🎯 投注记录</a></li>
                    <li><a href="{{ url(env('ADMIN_PATH', 'admin') . '/games') }}" class="{{ request()->is(env('ADMIN_PATH', 'admin') . '/games*') ? 'active' : '' }}">🎮 游戏记录</a></li>
                    <li><a href="{{ url(env('ADMIN_PATH', 'admin') . '/settings') }}" class="{{ request()->is(env('ADMIN_PATH', 'admin') . '/settings*') ? 'active' : '' }}">⚙️ 系统设置</a></li>
                    <li><a href="{{ url('/game') }}">🎯 返回游戏</a></li>
                    <li><a href="#" onclick="logout()">🚪 退出登录</a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="content-title">@yield('title', '仪表盘')</h1>
            </div>

            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    {{ session('error') }}
                </div>
            @endif

            <div class="content-body">
                @yield('content')
            </div>
        </main>
    </div>

    <script>
        // 退出登录
        async function logout() {
            if (confirm('确定要退出登录吗？')) {
                try {
                    const response = await fetch('/logout', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });
                    
                    if (response.ok) {
                        window.location.href = '/';
                    }
                } catch (error) {
                    console.error('退出失败:', error);
                }
            }
        }

        // 确认删除
        function confirmDelete(message = '确定要删除吗？') {
            return confirm(message);
        }
    </script>
</body>
</html>
