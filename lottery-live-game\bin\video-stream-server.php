<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Workerman\Worker;

class VideoStreamServer
{
    private $rtspUrl;
    private $ffmpegProcess;
    private $isRunning = false;
    private $frameBuffer = '';
    private $websocketUrl = 'http://localhost:8081/broadcast-frame';
    
    public function __construct($rtspUrl = null)
    {
        // 默认RTSP地址，可以通过环境变量配置
        $this->rtspUrl = $rtspUrl ?: $this->getDefaultRtspUrl();
    }
    
    private function getDefaultRtspUrl()
    {
        // 从环境变量或配置文件读取RTSP地址
        $host = $_ENV['CAMERA_HOST'] ?? '*************';
        $username = $_ENV['CAMERA_USERNAME'] ?? 'admin';
        $password = $_ENV['CAMERA_PASSWORD'] ?? 'admin123';
        $port = $_ENV['CAMERA_PORT'] ?? '554';
        $path = $_ENV['CAMERA_PATH'] ?? '/stream1';
        
        return "rtsp://{$username}:{$password}@{$host}:{$port}{$path}";
    }
    
    public function start()
    {
        echo "[" . date('Y-m-d H:i:s') . "] 启动视频流服务器...\n";
        echo "[" . date('Y-m-d H:i:s') . "] RTSP地址: {$this->rtspUrl}\n";
        
        // 测试RTSP连接
        if (!$this->testRtspConnection()) {
            echo "[ERROR] RTSP连接测试失败，使用模拟视频流\n";
            $this->startSimulatedStream();
            return;
        }
        
        $this->startRealStream();
    }
    
    private function testRtspConnection()
    {
        $cmd = "ffprobe -v quiet -print_format json -show_streams \"{$this->rtspUrl}\" 2>&1";
        $output = shell_exec($cmd);
        
        return strpos($output, '"streams"') !== false;
    }
    
    private function startRealStream()
    {
        echo "[" . date('Y-m-d H:i:s') . "] 启动真实摄像头流...\n";
        
        // FFmpeg命令：RTSP → JPEG帧流
        $cmd = "ffmpeg -i \"{$this->rtspUrl}\" " .
               "-vf \"scale=1280:720\" " .        // 缩放到720p
               "-r 30 " .                         // 30fps
               "-f image2pipe " .                 // 输出为图片流
               "-vcodec mjpeg " .                 // JPEG编码
               "-q:v 3 " .                        // 高质量
               "- 2>/dev/null";                   // 忽略错误输出
        
        $this->ffmpegProcess = popen($cmd, 'r');
        
        if (!$this->ffmpegProcess) {
            echo "[ERROR] 无法启动FFmpeg进程\n";
            return;
        }
        
        $this->isRunning = true;
        $this->processVideoFrames();
    }
    
    private function startSimulatedStream()
    {
        echo "[" . date('Y-m-d H:i:s') . "] 启动模拟视频流...\n";
        
        // 创建模拟视频帧
        $this->isRunning = true;
        
        while ($this->isRunning) {
            $frame = $this->generateSimulatedFrame();
            $this->sendFrameToWebSocket($frame);
            usleep(33333); // 约30fps (1/30秒 = 33333微秒)
        }
    }
    
    private function generateSimulatedFrame()
    {
        // 创建一个简单的测试图像
        $width = 1280;
        $height = 720;
        
        $image = imagecreate($width, $height);
        
        // 背景色（深蓝色）
        $bg = imagecolorallocate($image, 20, 30, 50);
        imagefill($image, 0, 0, $bg);
        
        // 文字颜色（白色）
        $white = imagecolorallocate($image, 255, 255, 255);
        $cyan = imagecolorallocate($image, 0, 255, 255);
        
        // 添加时间戳
        $time = date('Y-m-d H:i:s');
        imagestring($image, 5, 50, 50, "彩票直播游戏 - 模拟视频流", $white);
        imagestring($image, 4, 50, 100, "当前时间: {$time}", $cyan);
        
        // 添加一些动态元素
        $frame_count = time() % 100;
        imagestring($image, 3, 50, 150, "帧计数: {$frame_count}", $white);
        
        // 绘制一些动态图形
        $x = 100 + sin(time()) * 50;
        $y = 300 + cos(time()) * 30;
        imagefilledellipse($image, $x, $y, 50, 50, $cyan);
        
        // 转换为JPEG
        ob_start();
        imagejpeg($image, null, 80);
        $frameData = ob_get_contents();
        ob_end_clean();
        
        imagedestroy($image);
        
        return $frameData;
    }
    
    private function processVideoFrames()
    {
        echo "[" . date('Y-m-d H:i:s') . "] 开始处理视频帧...\n";
        
        while ($this->isRunning && !feof($this->ffmpegProcess)) {
            // 读取JPEG帧数据
            $chunk = fread($this->ffmpegProcess, 8192);
            
            if ($chunk === false || strlen($chunk) === 0) {
                usleep(1000); // 1ms
                continue;
            }
            
            $this->frameBuffer .= $chunk;
            
            // 查找JPEG帧边界
            $this->extractFramesFromBuffer();
        }
        
        echo "[" . date('Y-m-d H:i:s') . "] 视频流处理结束\n";
    }
    
    private function extractFramesFromBuffer()
    {
        // JPEG文件开始标记: FF D8
        // JPEG文件结束标记: FF D9
        
        while (true) {
            $start = strpos($this->frameBuffer, "\xFF\xD8");
            if ($start === false) {
                break;
            }
            
            $end = strpos($this->frameBuffer, "\xFF\xD9", $start);
            if ($end === false) {
                break;
            }
            
            // 提取完整的JPEG帧
            $frameLength = $end - $start + 2;
            $frameData = substr($this->frameBuffer, $start, $frameLength);
            
            // 发送帧到WebSocket
            $this->sendFrameToWebSocket($frameData);
            
            // 从缓冲区移除已处理的帧
            $this->frameBuffer = substr($this->frameBuffer, $end + 2);
        }
    }
    
    private function sendFrameToWebSocket($frameData)
    {
        // 使用cURL发送帧数据到WebSocket服务器
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->websocketUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $frameData,
            CURLOPT_HTTPHEADER => ['Content-Type: application/octet-stream'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 1,
            CURLOPT_CONNECTTIMEOUT => 1,
        ]);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            echo "[WARNING] 发送视频帧失败: HTTP {$httpCode}\n";
        }
    }
    
    public function stop()
    {
        echo "[" . date('Y-m-d H:i:s') . "] 停止视频流服务器...\n";
        
        $this->isRunning = false;
        
        if ($this->ffmpegProcess) {
            pclose($this->ffmpegProcess);
            $this->ffmpegProcess = null;
        }
    }
}

// 创建Worker来运行视频流服务器
$video_worker = new Worker();
$video_worker->count = 1;
$video_worker->name = 'VideoStream';

$video_worker->onWorkerStart = function() {
    $streamServer = new VideoStreamServer();
    $streamServer->start();
};

// 处理信号
$video_worker->onWorkerStop = function() {
    echo "[" . date('Y-m-d H:i:s') . "] 视频流服务器停止\n";
};

echo "视频流服务器启动中...\n";
echo "使用 Ctrl+C 停止服务器\n";

Worker::runAll();
