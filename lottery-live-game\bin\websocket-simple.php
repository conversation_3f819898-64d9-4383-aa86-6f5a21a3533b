<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Workerman\Worker;
use Workerman\Connection\TcpConnection;

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// WebSocket服务器
$ws_worker = new Worker("websocket://0.0.0.0:8080");
$ws_worker->count = 1; // Windows只支持单进程
$ws_worker->name = 'LotteryWebSocket';

// 存储所有连接
global $connections;
$connections = [];

// 客户端连接时
$ws_worker->onConnect = function(TcpConnection $connection) {
    global $connections;
    $connections[$connection->id] = $connection;
    
    echo "[" . date('Y-m-d H:i:s') . "] 新用户连接: {$connection->id} (总连接数: " . count($connections) . ")\n";
    
    // 发送欢迎消息
    $connection->send(json_encode([
        'type' => 'welcome',
        'data' => [
            'connection_id' => $connection->id,
            'server_time' => time(),
            'online_users' => count($connections)
        ]
    ]));
};

// 收到客户端消息时
$ws_worker->onMessage = function(TcpConnection $connection, $data) {
    global $connections;
    
    $message = json_decode($data, true);
    if (!$message) {
        return;
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] 收到消息: " . $data . "\n";
    
    switch ($message['type']) {
        case 'heartbeat':
            // 心跳响应
            $connection->send(json_encode([
                'type' => 'pong',
                'timestamp' => microtime(true)
            ]));
            break;
            
        case 'join_game':
            // 用户加入游戏
            $connection->user_id = $message['user_id'] ?? null;
            echo "[" . date('Y-m-d H:i:s') . "] 用户 {$connection->user_id} 加入游戏\n";
            break;
            
        case 'bet':
            // 处理下注（这里只是广播，实际下注逻辑在HTTP API中）
            broadcastToAll([
                'type' => 'new_bet',
                'data' => [
                    'user_id' => $connection->user_id,
                    'bet_type' => $message['bet_type'],
                    'bet_amount' => $message['bet_amount'],
                    'timestamp' => time()
                ]
            ], $connections);
            break;
            
        case 'video_frame':
            // 广播视频帧
            broadcastToAll([
                'type' => 'video_frame',
                'data' => $message['data'],
                'timestamp' => microtime(true)
            ], $connections);
            break;
    }
};

// 客户端断开连接时
$ws_worker->onClose = function(TcpConnection $connection) {
    global $connections;
    unset($connections[$connection->id]);
    
    echo "[" . date('Y-m-d H:i:s') . "] 用户断开: {$connection->id} (剩余连接数: " . count($connections) . ")\n";
    
    // 广播在线人数更新
    broadcastToAll([
        'type' => 'online_update',
        'data' => ['online_users' => count($connections)]
    ], $connections);
};

// 广播消息到所有连接
function broadcastToAll($message, $connections, $encode = true) {
    if (empty($connections)) {
        return;
    }
    
    $data = $encode ? json_encode($message) : $message;
    
    foreach ($connections as $conn) {
        try {
            $conn->send($data);
        } catch (Exception $e) {
            echo "[ERROR] 发送消息失败: " . $e->getMessage() . "\n";
        }
    }
}

// 定时任务 - 每10秒发送游戏状态更新
\Workerman\Lib\Timer::add(10, function() use (&$connections) {
    $gameStatus = [
        'type' => 'game_status',
        'data' => [
            'server_time' => time(),
            'online_users' => count($connections),
            'round_number' => rand(1000, 9999),
            'remaining_time' => rand(5, 30)
        ]
    ];
    
    broadcastToAll($gameStatus, $connections);
});

// 模拟视频帧发送 - 每秒发送一次模拟帧
\Workerman\Lib\Timer::add(1, function() use (&$connections) {
    if (empty($connections)) {
        return;
    }
    
    // 创建简单的模拟视频帧数据
    $frameData = base64_encode("模拟视频帧数据 - " . date('H:i:s'));
    
    $message = [
        'type' => 'video_frame',
        'data' => $frameData,
        'timestamp' => microtime(true)
    ];
    
    broadcastToAll($message, $connections);
});

echo "WebSocket服务器启动成功！\n";
echo "监听地址: ws://0.0.0.0:8080\n";
echo "按 Ctrl+C 停止服务器\n\n";

// 启动Worker
Worker::runAll();
