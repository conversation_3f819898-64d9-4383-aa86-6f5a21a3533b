@echo off
echo ========================================
echo 彩票直播游戏系统启动脚本
echo ========================================

echo.
echo [1/4] 检查PHP环境...
php --version
if %errorlevel% neq 0 (
    echo 错误: PHP未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo [2/5] 生成应用密钥...
php artisan key:generate

echo.
echo [3/5] 检查数据库连接...
php check-database.php
if %errorlevel% neq 0 (
    echo.
    echo 数据库配置有问题，请按照上面的提示解决后重新运行
    pause
    exit /b 1
)

echo.
echo [4/5] 运行数据库迁移...
php artisan migrate --force

echo.
echo [5/5] 创建测试用户...
php artisan db:seed --class=UserSeeder

echo.
echo [6/6] 启动服务器...

echo.
echo 启动WebSocket服务器 (端口8080)...
start "WebSocket服务器" cmd /k "php bin/websocket-server.php"

echo.
echo 等待2秒...
timeout /t 2 /nobreak > nul

echo.
echo 启动视频流服务器 (模拟摄像头)...
start "视频流服务器" cmd /k "php bin/video-stream-server.php"

echo.
echo 等待2秒...
timeout /t 2 /nobreak > nul

echo.
echo 启动Laravel开发服务器 (端口8000)...
start "Laravel服务器" cmd /k "php artisan serve"

echo.
echo ========================================
echo 所有服务器启动完成！
echo ========================================
echo.
echo 访问地址: http://localhost:8000
echo 测试账号: 6117
echo 测试密码: 6677
echo.
echo 后台管理: http://localhost:8000/%ADMIN_PATH%
echo 后台账号: admin
echo 后台密码: admin123
echo.
echo WebSocket服务器: ws://localhost:8080
echo 视频流API: http://localhost:8081
echo.
echo 按任意键退出...
pause > nul
