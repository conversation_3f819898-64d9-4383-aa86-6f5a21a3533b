<?php
/**
 * 彩票直播游戏系统 - 主入口文件
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Core\Application;
use App\Core\Router;
use App\Controllers\AuthController;
use App\Controllers\GameController;
use App\Controllers\LiveController;

// 加载环境变量
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 创建应用实例
$app = new Application();

// 配置路由
$router = new Router();

// 认证路由
$router->get('/', [AuthController::class, 'loginPage']);
$router->post('/login', [AuthController::class, 'login']);
$router->post('/logout', [AuthController::class, 'logout']);

// 游戏路由
$router->get('/game', [GameController::class, 'index']);
$router->get('/api/game/status', [GameController::class, 'getStatus']);
$router->post('/api/game/bet', [GameController::class, 'placeBet']);

// 直播路由
$router->get('/api/live/stream', [LiveController::class, 'getStreamUrl']);

// 静态文件路由
$router->get('/assets/{file}', function($file) {
    $filePath = __DIR__ . '/assets/' . $file;
    if (file_exists($filePath)) {
        $mimeType = mime_content_type($filePath);
        header('Content-Type: ' . $mimeType);
        readfile($filePath);
        exit;
    }
    http_response_code(404);
    echo '文件未找到';
});

// 运行应用
$app->run($router);
