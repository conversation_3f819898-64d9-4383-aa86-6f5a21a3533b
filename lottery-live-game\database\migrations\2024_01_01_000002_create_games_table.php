<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('games', function (Blueprint $table) {
            $table->id();
            $table->integer('round_number');
            $table->integer('version_number');
            $table->json('result_numbers')->nullable(); // 开奖号码数组
            $table->timestamp('start_time');
            $table->timestamp('end_time');
            $table->enum('status', ['waiting', 'betting', 'drawing', 'finished'])->default('waiting');
            $table->decimal('total_bets', 12, 2)->default(0.00);
            $table->integer('total_players')->default(0);
            $table->timestamps();
            
            $table->index(['round_number', 'version_number']);
            $table->index(['status', 'start_time']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('games');
    }
};
