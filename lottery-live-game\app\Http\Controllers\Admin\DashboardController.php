<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Game;
use App\Models\Bet;
use App\Models\UserSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // 统计数据
        $stats = [
            'total_users' => User::count(),
            'online_users' => UserSession::online()->count(),
            'total_games' => Game::count(),
            'total_bets' => Bet::sum('bet_amount'),
            'total_wins' => Bet::where('status', 'win')->sum('potential_win'),
            'today_bets' => Bet::whereDate('created_at', today())->sum('bet_amount'),
            'today_wins' => Bet::whereDate('created_at', today())->where('status', 'win')->sum('potential_win'),
        ];

        // 最近游戏
        $recent_games = Game::with(['bets' => function($q) {
            $q->limit(5);
        }])->orderBy('id', 'desc')->limit(10)->get();

        // 最近用户
        $recent_users = User::orderBy('created_at', 'desc')->limit(10)->get();

        // 在线用户
        $online_users = UserSession::online()->with('user')->get();

        return view('admin.dashboard', compact('stats', 'recent_games', 'recent_users', 'online_users'));
    }

    public function users(Request $request)
    {
        $query = User::query();

        // 搜索
        if ($request->search) {
            $query->where('username', 'like', '%' . $request->search . '%');
        }

        // 状态过滤
        if ($request->status) {
            $query->where('status', $request->status);
        }

        $users = $query->withCount('bets')
                      ->withSum('bets', 'bet_amount')
                      ->orderBy('created_at', 'desc')
                      ->paginate(20);

        return view('admin.users', compact('users'));
    }

    public function userDetail(User $user)
    {
        $user->load(['bets' => function($q) {
            $q->with('game')->orderBy('created_at', 'desc')->limit(50);
        }, 'sessions' => function($q) {
            $q->orderBy('last_activity', 'desc')->limit(20);
        }]);

        $stats = [
            'total_bets' => $user->bets()->sum('bet_amount'),
            'total_wins' => $user->bets()->where('status', 'win')->sum('potential_win'),
            'win_rate' => $user->bets()->count() > 0 ? 
                round($user->bets()->where('status', 'win')->count() / $user->bets()->count() * 100, 2) : 0,
            'profit_loss' => $user->bets()->where('status', 'win')->sum('potential_win') - $user->bets()->sum('bet_amount'),
        ];

        return view('admin.user-detail', compact('user', 'stats'));
    }

    public function adjustBalance(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric',
            'type' => 'required|in:add,subtract',
            'reason' => 'required|string|max:255'
        ]);

        $amount = $request->amount;
        if ($request->type === 'subtract') {
            if ($user->balance < $amount) {
                return back()->with('error', '用户余额不足');
            }
            $amount = -$amount;
        }

        $user->addBalance($amount, $request->reason);

        return back()->with('success', '余额调整成功');
    }

    public function bets(Request $request)
    {
        $query = Bet::with(['user', 'game']);

        // 日期过滤
        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // 状态过滤
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // 用户过滤
        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        $bets = $query->orderBy('created_at', 'desc')->paginate(50);

        return view('admin.bets', compact('bets'));
    }

    public function games(Request $request)
    {
        $query = Game::withCount('bets')->withSum('bets', 'bet_amount');

        // 状态过滤
        if ($request->status) {
            $query->where('status', $request->status);
        }

        $games = $query->orderBy('created_at', 'desc')->paginate(30);

        return view('admin.games', compact('games'));
    }

    public function settings()
    {
        return view('admin.settings');
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'game_round_duration' => 'required|integer|min:10|max:300',
            'default_user_balance' => 'required|numeric|min:0',
        ]);

        // 这里可以保存设置到数据库或配置文件
        // 暂时返回成功消息
        return back()->with('success', '设置更新成功');
    }

    public function changeUserStatus(Request $request, User $user)
    {
        $request->validate([
            'status' => 'required|in:active,banned,suspended'
        ]);

        $user->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => '用户状态更新成功'
        ]);
    }
}
