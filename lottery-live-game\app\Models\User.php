<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'password',
        'balance',
        'status',
        'last_login_at',
        'last_login_ip',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'balance' => 'decimal:2',
        ];
    }

    // 关联关系
    public function bets()
    {
        return $this->hasMany(Bet::class);
    }

    public function sessions()
    {
        return $this->hasMany(UserSession::class);
    }

    // 业务方法
    public function isOnline()
    {
        return $this->sessions()
            ->where('is_online', true)
            ->where('last_activity', '>', now()->subMinutes(5))
            ->exists();
    }

    public function addBalance($amount, $reason = null)
    {
        $this->increment('balance', $amount);

        \Log::info('用户余额变动', [
            'user_id' => $this->id,
            'amount' => $amount,
            'new_balance' => $this->fresh()->balance,
            'reason' => $reason
        ]);
    }

    public function subtractBalance($amount, $reason = null)
    {
        if ($this->balance < $amount) {
            throw new \Exception('余额不足');
        }

        $this->decrement('balance', $amount);

        \Log::info('用户余额变动', [
            'user_id' => $this->id,
            'amount' => -$amount,
            'new_balance' => $this->fresh()->balance,
            'reason' => $reason
        ]);
    }
}
