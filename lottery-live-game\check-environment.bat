@echo off
echo ========================================
echo 环境检查脚本
echo ========================================

echo.
echo [1/4] 检查PHP...
php --version
if %errorlevel% neq 0 (
    echo ✗ PHP未安装或未添加到PATH
    echo.
    echo 解决方案：
    echo 1. 下载PHP: https://windows.php.net/download/
    echo 2. 解压到 C:\php
    echo 3. 将 C:\php 添加到系统PATH环境变量
    echo 4. 重启命令行窗口
    echo.
    pause
    exit /b 1
) else (
    echo ✓ PHP环境正常
)

echo.
echo [2/4] 检查Composer...
composer --version
if %errorlevel% neq 0 (
    echo ✗ Composer未安装
    echo.
    echo 解决方案：
    echo 1. 下载Composer: https://getcomposer.org/download/
    echo 2. 运行安装程序
    echo 3. 重启命令行窗口
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Composer环境正常
)

echo.
echo [3/4] 检查vendor目录...
if not exist "vendor" (
    echo ✗ vendor目录不存在，正在安装依赖...
    composer install
    if %errorlevel% neq 0 (
        echo ✗ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✓ vendor目录存在
)

echo.
echo [4/4] 检查.env文件...
if not exist ".env" (
    echo ✗ .env文件不存在，正在复制...
    copy ".env.example" ".env"
    if %errorlevel% neq 0 (
        echo ✗ .env文件创建失败
        pause
        exit /b 1
    )
) else (
    echo ✓ .env文件存在
)

echo.
echo ========================================
echo 环境检查完成！
echo ========================================
echo.
echo 现在可以运行: start-servers.bat
echo.
pause
